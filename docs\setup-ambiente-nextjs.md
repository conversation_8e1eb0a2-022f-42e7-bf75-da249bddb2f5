
# 🧱 Setup de Ambiente Profissional Next.js + React (VS Code / Local / Sem Git)

> **Agente**, siga as instruções abaixo para configurar um ambiente de desenvolvimento completo, local e profissional utilizando **Next.js com React**. Este ambiente é voltado para uso **no VS Code**, **sem integração com Git/GitHub**, e com foco em **qualidade, desempenho e escalabilidade**.

---

## ✅ Etapas de Configuração

### 1. Inicialize um novo projeto Next.js com suporte a TypeScript

- Crie o projeto base com suporte à tipagem estática desde o início.

---

### 2. Organize a estrutura de pastas profissional

- Dentro da pasta `src/`, crie os seguintes diretórios:
  - `components`
  - `features`
  - `hooks`
  - `lib`
  - `pages`
  - `styles`
  - `types`
  - `utils`

---

### 3. Configure ferramentas de qualidade de código

- Instale e configure:
  - ESLint
  - Prettier
  - Suporte a TypeScript
- Ative verificação de erros e formatação automática ao salvar.

---

### 4. Instale e configure TailwindCSS

- Configure com PostCSS e Autoprefixer.
- Habilite suporte a utilitários e responsividade.

---

### 5. Instale bibliotecas modernas para produtividade

Inclua as seguintes bibliotecas:

- **axios** – Requisições HTTP
- **zustand** – Gerenciamento de estado
- **swr** – Fetching reativo
- **clsx** – Composição de classes
- **framer-motion** – Animações
- **@headlessui/react** – UI acessível
- **@heroicons/react** – Ícones

---

### 6. Configure o VS Code para produtividade

- Crie `.vscode/settings.json` com:
  - ESLint ativo
  - Formatação automática
  - Prettier como formatador padrão
- Crie `.vscode/extensions.json` com recomendações:
  - ESLint
  - Prettier
  - Tailwind CSS
  - Auto Rename/Close Tag
  - Import Cost

---

### 7. Não use Git ou GitHub

- **Não** inicialize repositório Git
- **Não** crie `.gitignore`, `.husky`, ou `lint-staged`
- Ambiente é 100% local e sem versionamento

---

### 8. Configure variáveis de ambiente com arquivos `.env`

- Use arquivos `.env.local` para armazenar variáveis sensíveis localmente.

---

### 9. Verifique se o projeto está pronto

- Execute o projeto localmente (`npm run dev`)
- Confirme que:
  - A estrutura está limpa
  - Código é validado e formatado automaticamente
  - Tudo roda corretamente no navegador

---

## ✅ Resultado Esperado

- Ambiente local, moderno e padronizado
- Código limpo, escalável e validado automaticamente
- Zero dependência de Git ou repositórios remotos
- Pronto para iniciar o desenvolvimento profissional
